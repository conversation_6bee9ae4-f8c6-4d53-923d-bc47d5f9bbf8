import * as THREE from 'three';

const DEFAULT_TEXTUREURL = '/particles/dot.png';

export default class ParticleInstanceMesh {
  constructor(parent, particleCount = 1000) {
    this.maxCount = particleCount;
    this.poolUseIndex = 0;
    this.camera = null; // 存储摄像机引用

    const particleGeometry = new THREE.PlaneGeometry(0.5, 0.5);

    // 尝试加载纹理，失败时使用程序化纹理
    const textureLoader = new THREE.TextureLoader();

    const texture = textureLoader.load(DEFAULT_TEXTUREURL, function (loadedTexture) {
      console.log('Particle texture loaded successfully');
    });

    const particleMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      opacity: 0.1,
      transparent: true,
      side: THREE.FrontSide,
      depthWrite: false,
      blending: THREE.AdditiveBlending,
      // blending: THREE.NormalBlending,
      // blending: THREE.SubtractiveBlending,
      // blending: THREE.MultiplyBlending,
      // blending: THREE.CustomBlending,
    });
    this.instancedMesh = new THREE.InstancedMesh(particleGeometry, particleMaterial, particleCount);
    const colors = new Float32Array(particleCount * 3);
    // 初始化为白色而不是黑色
    for (let i = 0; i < particleCount * 3; i++) {
      colors[i] = 1.0;
    }
    this.instancedMesh.instanceColor = new THREE.InstancedBufferAttribute(colors, 3);
    // this.instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
    const matrix = new THREE.Matrix4();
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();
    //初始所有实例不可见 。  scale = 0
    for (let i = 0; i < particleCount; i++) {
      matrix.compose(position, quaternion, scale);
      this.instancedMesh.setMatrixAt(i, matrix);
    }

    this.instancedMesh.instanceMatrix.needsUpdate = true;
    parent.add(this.instancedMesh);
  }

  createInstance() {
    this.poolUseIndex++;
    this.poolUseIndex = this.poolUseIndex % this.maxCount;
    return this.poolUseIndex;
  }

  updateInstance(instanceIndex, position, quaternion, scale = 1) {
    const matrix = new THREE.Matrix4();
    const scaleVec = new THREE.Vector3();
    scaleVec.set(scale, scale, scale);
    matrix.compose(position, quaternion, scaleVec);
    this.instancedMesh.setMatrixAt(instanceIndex, matrix);
    this.instancedMesh.instanceMatrix.needsUpdate = true;
  }

  destroyInstance(instanceIndex) {
    //将实例变成不可见
    const matrix = new THREE.Matrix4();
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();
    matrix.compose(position, quaternion, scale);
    this.instancedMesh.setMatrixAt(instanceIndex, matrix);
    this.instancedMesh.instanceMatrix.needsUpdate = true;
  }

  // 按索引设置粒子颜色
  setParticleColorByIndex(index, color) {
    const threeColor = new THREE.Color(color.r, color.g, color.b);
    this.instancedMesh.setColorAt(index, threeColor);
    this.instancedMesh.instanceColor.needsUpdate = true;
  }

  destroy() {
    // this.instancedMesh.removeFromParent();
  }
}
